.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	outline: none;
	cursor: pointer;
	background-image: linear-gradient(to top, #d8d9db 0%, #fff 80%, #fdfdfd 100%);
	border-radius: 100em;
	border: 1px solid #8f9092;
	color: #606060;
	text-shadow: 0 1px #fff;
	transition: all 0.2s ease;
	padding: 0.5em 1em 0.5em 1em;
	box-sizing: border-box;
	min-width: 7em;
	white-space: nowrap;
}

.btn:active {
	box-shadow:
		inset 0 0 5px 3px #bbb,
		inset 0 0 30px #ddd;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}
